// Simple test to verify API endpoints
const API_BASE_URL = 'https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net';

async function testAPI() {
  console.log('Testing API endpoints...');

  try {
    // Test the test endpoint
    console.log('\n1. Testing /authapi/test endpoint...');
    const testResponse = await fetch(`${API_BASE_URL}/authapi/test`);
    console.log('Status:', testResponse.status);
    const testData = await testResponse.text();
    console.log('Response:', testData);

    // Test login with invalid credentials
    console.log('\n2. Testing /authapi/login with invalid credentials...');
    const loginResponse = await fetch(`${API_BASE_URL}/authapi/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    });
    console.log('Login Status:', loginResponse.status);
    const loginData = await loginResponse.text();
    console.log('Login Response:', loginData);

    // Test login with existing user (from the test response)
    console.log('\n3. Testing /authapi/login with existing user...');
    const validLoginResponse = await fetch(`${API_BASE_URL}/authapi/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123' // Common password, might work
      })
    });
    console.log('Valid Login Status:', validLoginResponse.status);
    const validLoginData = await validLoginResponse.text();
    console.log('Valid Login Response:', validLoginData);

    // Test signup with new user
    console.log('\n4. Testing /authapi/signup with new user...');
    const signupResponse = await fetch(`${API_BASE_URL}/authapi/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test User 2',
        email: '<EMAIL>',
        password: 'TestPassword123',
        roleId: '686cc04c1237a82fc74b4a6a' // Using the roleId from the test response
      })
    });
    console.log('Signup Status:', signupResponse.status);
    const signupData = await signupResponse.text();
    console.log('Signup Response:', signupData);

    // If signup successful, try login
    if (signupResponse.status === 200 || signupResponse.status === 201) {
      console.log('\n5. Testing login with newly created user...');
      const newUserLoginResponse = await fetch(`${API_BASE_URL}/authapi/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'TestPassword123'
        })
      });
      console.log('New User Login Status:', newUserLoginResponse.status);
      const newUserLoginData = await newUserLoginResponse.text();
      console.log('New User Login Response:', newUserLoginData);

      // Try to parse as JSON to see the structure
      try {
        const parsedResponse = JSON.parse(newUserLoginData);
        console.log('Parsed JSON structure:', JSON.stringify(parsedResponse, null, 2));
      } catch (e) {
        console.log('Response is not valid JSON');
      }
    }

  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testAPI();